//
//  WebSocketManager.swift
//  TinyVoice
//
//  Created by <PERSON> on 8/9/25.
//

import Foundation
import AVFoundation
import Combine

class WebSocketManager: NSObject, ObservableObject {
    @Published var isListening = false
    @Published var wakeWordStatus = "Initializing wake word detection..."

    private var webSocketTask: URLSessionWebSocketTask?
    private var audioEngine: AVAudioEngine?
    private var inputNode: AVAudioInputNode?
    private var isListeningAfterWakeWord = false

    private let wsURL = URL(string: "ws://192.168.50.43:8000/ws/wakeword")!
    private let sampleRate: Double = 16000 // Wake word detection expects 16kHz

    // Audio effects
    private var tunePlayer: AVAudioPlayer?
    private var punchPlayer: AVAudioPlayer?

    // Reconnection logic
    private var reconnectionTimer: Timer?
    private var reconnectionAttempts = 0
    private let maxReconnectionAttempts = 5
    
    override init() {
        super.init()
        setupAudioPlayers()
        setupNotificationObservers()
    }

    private func setupNotificationObservers() {
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleSessionReset),
            name: .sessionReset,
            object: nil
        )
    }

    @objc private func handleSessionReset() {
        // Reset WebSocketManager state after assistant finishes speaking
        isListeningAfterWakeWord = false

        DispatchQueue.main.async {
            self.wakeWordStatus = "Listening for \"Scarlett\"..."
        }

        print("WebSocketManager: Session reset - ready for next wake word")
    }
    
    private func setupAudioPlayers() {
        // Setup tune player for wake word detection
        if let tunePath = Bundle.main.path(forResource: "tune", ofType: "wav") {
            do {
                tunePlayer = try AVAudioPlayer(contentsOf: URL(fileURLWithPath: tunePath))
                tunePlayer?.volume = 1.0
            } catch {
                print("Error setting up tune player: \(error)")
            }
        }
        
        // Setup punch player for execution keyword
        if let punchPath = Bundle.main.path(forResource: "punch", ofType: "mp3") {
            do {
                punchPlayer = try AVAudioPlayer(contentsOf: URL(fileURLWithPath: punchPath))
                punchPlayer?.volume = 0.7
            } catch {
                print("Error setting up punch player: \(error)")
            }
        }
    }
    
    func toggleWakeWordListening() {
        if isListening {
            stopListening()
        } else {
            startListening()
        }
    }
    
    private func startListening() {
        guard !isListening else { return }

        do {
            connectWebSocket()
            setupAudioProcessing()

            DispatchQueue.main.async {
                self.isListening = true
                self.wakeWordStatus = "Listening for \"Scarlett\"..."
            }
        } catch {
            DispatchQueue.main.async {
                self.wakeWordStatus = "Audio session setup failed: \(error.localizedDescription)"
            }
        }
    }
    
    private func stopListening() {
        DispatchQueue.main.async {
            self.isListening = false
            self.wakeWordStatus = "Wake word detection offline"
        }
        
        disconnect()
        cleanupAudioProcessing()
    }
    
    private func connectWebSocket() {
        let session = URLSession(configuration: .default)
        webSocketTask = session.webSocketTask(with: wsURL)
        webSocketTask?.resume()

        // Reset reconnection attempts on successful connection
        reconnectionAttempts = 0
        reconnectionTimer?.invalidate()

        receiveMessage()

        // Send periodic pings to keep connection alive
        startPingTimer()
    }
    
    private func receiveMessage() {
        webSocketTask?.receive { [weak self] result in
            switch result {
            case .success(let message):
                switch message {
                case .string(let text):
                    self?.handleWebSocketMessage(text)
                case .data(_):
                    break
                @unknown default:
                    break
                }
                self?.receiveMessage() // Continue receiving
                
            case .failure(let error):
                print("WebSocket receive error: \(error)")
                self?.handleConnectionError()
            }
        }
    }
    
    private func handleWebSocketMessage(_ text: String) {
        guard let data = text.data(using: .utf8),
              let message = try? JSONSerialization.jsonObject(with: data) as? [String: Any],
              let type = message["type"] as? String else { return }
        
        DispatchQueue.main.async {
            switch type {
            case "wakeword_detected":
                self.handleWakeWordDetected()
            case "execution_keyword_detected":
                self.handleExecutionKeywordDetected()
            case "pong":
                break // Keep-alive response
            default:
                break
            }
        }
    }
    
    private func handleWakeWordDetected() {
        guard !isListeningAfterWakeWord else { return }
        
        print("Wake word detected! Starting continuous listening...")
        wakeWordStatus = "Wake word detected! Please speak... (say \"skynet\" to finish)"
        
        // Play tune
        tunePlayer?.stop()
        tunePlayer?.currentTime = 0
        tunePlayer?.play()
        
        // Fade out tune after 250ms
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.25) {
            self.fadeOutTune()
        }
        
        isListeningAfterWakeWord = true
        updateBackendListeningState(true)
        
        // Notify AudioManager about wake word detection
        NotificationCenter.default.post(name: .wakeWordDetected, object: nil)
    }
    
    private func handleExecutionKeywordDetected() {
        guard isListeningAfterWakeWord else { return }
        
        print("Execution keyword detected!")
        wakeWordStatus = "Execution keyword detected! Processing..."
        
        // Play punch sound
        punchPlayer?.stop()
        punchPlayer?.currentTime = 0
        punchPlayer?.play()
        
        isListeningAfterWakeWord = false
        updateBackendListeningState(false)
        
        // Notify AudioManager about execution keyword
        NotificationCenter.default.post(name: .executionKeywordDetected, object: nil)
        
        // Return to listening state
        DispatchQueue.main.asyncAfter(deadline: .now() + 1) {
            self.wakeWordStatus = "Listening for \"Scarlett\"..."
        }
    }
    
    private func fadeOutTune() {
        guard let player = tunePlayer, player.isPlaying else { return }
        
        let fadeTimer = Timer.scheduledTimer(withTimeInterval: 0.025, repeats: true) { timer in
            if player.volume > 0.1 {
                player.volume -= 0.1
            } else {
                timer.invalidate()
                player.stop()
                player.currentTime = 0
                player.volume = 1.0
            }
        }
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.25) {
            fadeTimer.invalidate()
        }
    }
    
    private func setupAudioProcessing() {
        do {
            audioEngine = AVAudioEngine()
            guard let audioEngine = audioEngine else { return }

            inputNode = audioEngine.inputNode
            guard let inputNode = inputNode else { return }

            // Use the input node's native format first
            let inputFormat = inputNode.outputFormat(forBus: 0)
            print("Input format: \(inputFormat)")

            // Create a converter to convert to our desired format (16kHz mono)
            let desiredFormat = AVAudioFormat(standardFormatWithSampleRate: sampleRate, channels: 1)!
            print("Desired format: \(desiredFormat)")

            // Install tap with the input's native format
            inputNode.installTap(onBus: 0, bufferSize: 1536, format: inputFormat) { [weak self] buffer, _ in
                self?.processAudioForWakeWord(buffer, inputFormat: inputFormat, desiredFormat: desiredFormat)
            }

            try audioEngine.start()

        } catch {
            print("Error setting up audio processing: \(error)")
            DispatchQueue.main.async {
                self.wakeWordStatus = "Audio setup failed: \(error.localizedDescription)"
            }
        }
    }
    
    private func processAudioForWakeWord(_ buffer: AVAudioPCMBuffer, inputFormat: AVAudioFormat, desiredFormat: AVAudioFormat) {
        guard isListening,
              let webSocketTask = webSocketTask,
              webSocketTask.state == .running else { return }

        // Convert the buffer to the desired format if needed
        let convertedBuffer: AVAudioPCMBuffer

        if inputFormat.sampleRate == desiredFormat.sampleRate && inputFormat.channelCount == desiredFormat.channelCount {
            // No conversion needed
            convertedBuffer = buffer
        } else {
            // Need to convert format
            guard let converter = AVAudioConverter(from: inputFormat, to: desiredFormat),
                  let outputBuffer = AVAudioPCMBuffer(pcmFormat: desiredFormat, frameCapacity: AVAudioFrameCount(Double(buffer.frameLength) * desiredFormat.sampleRate / inputFormat.sampleRate)) else {
                print("Failed to create audio converter")
                return
            }

            var error: NSError?
            let status = converter.convert(to: outputBuffer, error: &error) { _, outStatus in
                outStatus.pointee = .haveData
                return buffer
            }

            if status == .error {
                print("Audio conversion error: \(error?.localizedDescription ?? "Unknown")")
                return
            }

            convertedBuffer = outputBuffer
        }

        // Convert to Int16 data for wake word detection
        guard let floatChannelData = convertedBuffer.floatChannelData?[0] else { return }
        let frameLength = Int(convertedBuffer.frameLength)

        var int16Data = [Int16]()
        int16Data.reserveCapacity(frameLength)

        for i in 0..<frameLength {
            let sample = floatChannelData[i]
            let clampedSample = max(-1.0, min(1.0, sample))
            let int16Sample = Int16(clampedSample < 0 ? clampedSample * 32768 : clampedSample * 32767)
            int16Data.append(int16Sample)
        }

        let audioData = Data(bytes: int16Data, count: frameLength * 2)
        let base64Data = audioData.base64EncodedString()

        let message: [String: Any] = [
            "type": "audio",
            "data": base64Data
        ]

        if let jsonData = try? JSONSerialization.data(withJSONObject: message),
           let jsonString = String(data: jsonData, encoding: .utf8) {
            webSocketTask.send(.string(jsonString)) { error in
                if let error = error {
                    print("WebSocket send error: \(error)")
                }
            }
        }
    }
    
    private func updateBackendListeningState(_ listening: Bool) {
        guard let webSocketTask = webSocketTask,
              webSocketTask.state == .running else { return }
        
        let message: [String: Any] = [
            "type": "listening_state",
            "is_listening": listening
        ]
        
        if let jsonData = try? JSONSerialization.data(withJSONObject: message),
           let jsonString = String(data: jsonData, encoding: .utf8) {
            webSocketTask.send(.string(jsonString)) { error in
                if let error = error {
                    print("WebSocket send error: \(error)")
                }
            }
        }
    }
    
    private func startPingTimer() {
        Timer.scheduledTimer(withTimeInterval: 30, repeats: true) { [weak self] _ in
            guard let self = self,
                  let webSocketTask = self.webSocketTask,
                  webSocketTask.state == .running else { return }
            
            let pingMessage = ["type": "ping"]
            if let jsonData = try? JSONSerialization.data(withJSONObject: pingMessage),
               let jsonString = String(data: jsonData, encoding: .utf8) {
                webSocketTask.send(.string(jsonString)) { _ in }
            }
        }
    }
    
    private func cleanupAudioProcessing() {
        audioEngine?.stop()
        inputNode?.removeTap(onBus: 0)
        audioEngine = nil
        inputNode = nil
    }
    
    private func handleConnectionError() {
        DispatchQueue.main.async {
            self.wakeWordStatus = "Connection lost. Reconnecting..."
        }

        guard isListening && reconnectionAttempts < maxReconnectionAttempts else {
            DispatchQueue.main.async {
                self.wakeWordStatus = "Wake word detection offline"
                self.isListening = false
            }
            return
        }

        reconnectionAttempts += 1
        let delay = min(pow(2.0, Double(reconnectionAttempts)), 30.0) // Exponential backoff, max 30 seconds

        reconnectionTimer?.invalidate()
        reconnectionTimer = Timer.scheduledTimer(withTimeInterval: delay, repeats: false) { [weak self] _ in
            if self?.isListening == true {
                self?.connectWebSocket()
            }
        }
    }

    func disconnect() {
        reconnectionTimer?.invalidate()
        reconnectionTimer = nil
        reconnectionAttempts = 0
        webSocketTask?.cancel()
        webSocketTask = nil
        cleanupAudioProcessing()
    }

    deinit {
        // Clean up notification observers
        NotificationCenter.default.removeObserver(self)
    }
}

// Notification names for communication with AudioManager
extension Notification.Name {
    static let wakeWordDetected = Notification.Name("wakeWordDetected")
    static let executionKeywordDetected = Notification.Name("executionKeywordDetected")
    static let sessionReset = Notification.Name("sessionReset")
}
