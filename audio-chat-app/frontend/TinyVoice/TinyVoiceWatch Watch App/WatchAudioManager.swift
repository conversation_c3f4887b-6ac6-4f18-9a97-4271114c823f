//
//  WatchAudioManager.swift
//  TinyVoiceWatch Watch App
//
//  Created by <PERSON> on 8/16/25.
//

import Foundation
import AVFoundation
import Combine

class WatchAudioManager: NSObject, ObservableObject {
    @Published var isRecording = false
    @Published var isProcessing = false
    @Published var status = "Ready to record"
    
    private var audioEngine: AVAudioEngine?
    private var inputNode: AVAudioInputNode?
    private var audioBuffer: [Float] = []
    private var recordingStartTime: Date?
    
    // Voice activity detection
    private var silenceTimer: Timer?
    private let silenceThreshold: Float = 0.01 // Amplitude threshold for silence
    private let silenceTimeout: TimeInterval = 2.0 // Seconds of silence before auto-stop
    private let maxRecordingDuration: TimeInterval = 30.0 // Max 30 seconds
    private var lastSoundTime: Date?
    
    // Audio session and playback
    private var audioPlayer: AVAudioPlayer?
    
    // API integration
    var apiManager: WatchAPIManager?
    
    // Audio format settings optimized for watchOS
    private let sampleRate: Double = 16000 // Lower sample rate for watch
    private let bufferSize: AVAudioFrameCount = 1024
    
    override init() {
        super.init()
        setupAudioSession()
    }
    
    func setupAudioSession() {
        do {
            let audioSession = AVAudioSession.sharedInstance()
            try audioSession.setCategory(.playAndRecord, mode: .default, options: [.defaultToSpeaker])
            try audioSession.setActive(true)
            
            // Request microphone permission
            audioSession.requestRecordPermission { [weak self] granted in
                DispatchQueue.main.async {
                    if granted {
                        self?.status = "Ready to record"
                    } else {
                        self?.status = "Microphone access denied"
                    }
                }
            }
        } catch {
            DispatchQueue.main.async {
                self.status = "Audio setup failed"
            }
        }
    }
    
    func startRecording() {
        guard !isRecording && !isProcessing else { return }
        
        do {
            audioEngine = AVAudioEngine()
            guard let audioEngine = audioEngine else { return }
            
            inputNode = audioEngine.inputNode
            guard let inputNode = inputNode else { return }
            
            // Clear previous buffer
            audioBuffer.removeAll()
            recordingStartTime = Date()
            lastSoundTime = Date()
            
            // Set up recording format (16kHz mono for efficiency)
            let recordingFormat = AVAudioFormat(standardFormatWithSampleRate: sampleRate, channels: 1)!
            
            inputNode.installTap(onBus: 0, bufferSize: bufferSize, format: recordingFormat) { [weak self] buffer, _ in
                self?.processAudioBuffer(buffer)
            }
            
            try audioEngine.start()
            
            DispatchQueue.main.async {
                self.isRecording = true
                self.status = "Recording... Speak now"
            }
            
            // Start silence detection timer
            startSilenceDetection()
            
            // Auto-stop after max duration
            DispatchQueue.main.asyncAfter(deadline: .now() + maxRecordingDuration) {
                if self.isRecording {
                    self.stopRecording()
                }
            }
            
        } catch {
            DispatchQueue.main.async {
                self.status = "Failed to start recording"
            }
        }
    }
    
    private func processAudioBuffer(_ buffer: AVAudioPCMBuffer) {
        guard let channelData = buffer.floatChannelData?[0] else { return }
        
        let frameCount = Int(buffer.frameLength)
        let samples = Array(UnsafeBufferPointer(start: channelData, count: frameCount))
        
        // Add to buffer
        audioBuffer.append(contentsOf: samples)
        
        // Check for voice activity
        let amplitude = samples.map { abs($0) }.max() ?? 0
        if amplitude > silenceThreshold {
            lastSoundTime = Date()
        }
    }
    
    private func startSilenceDetection() {
        silenceTimer?.invalidate()
        silenceTimer = Timer.scheduledTimer(withTimeInterval: 0.1, repeats: true) { [weak self] _ in
            guard let self = self, let lastSound = self.lastSoundTime else { return }
            
            let timeSinceLastSound = Date().timeIntervalSince(lastSound)
            if timeSinceLastSound >= self.silenceTimeout {
                DispatchQueue.main.async {
                    self.stopRecording()
                }
            }
        }
    }
    
    func stopRecording() {
        guard isRecording else { return }
        
        silenceTimer?.invalidate()
        silenceTimer = nil
        
        audioEngine?.stop()
        inputNode?.removeTap(onBus: 0)
        
        DispatchQueue.main.async {
            self.isRecording = false
            self.isProcessing = true
            self.status = "Processing..."
        }
        
        // Convert audio buffer to WAV data
        if !audioBuffer.isEmpty {
            let wavData = createWAVData(from: audioBuffer)
            sendAudioToAPI(wavData)
        } else {
            DispatchQueue.main.async {
                self.isProcessing = false
                self.status = "No audio recorded"
            }
        }
    }
    
    private func createWAVData(from samples: [Float]) -> Data {
        let sampleRate = Int32(self.sampleRate)
        let numChannels: Int16 = 1
        let bitsPerSample: Int16 = 16
        let bytesPerSample = Int(bitsPerSample / 8)
        let numSamples = samples.count
        
        // Convert float samples to 16-bit integers
        let int16Samples = samples.map { sample in
            Int16(max(-32768, min(32767, sample * 32767)))
        }
        
        var wavData = Data()
        
        // WAV header
        wavData.append("RIFF".data(using: .ascii)!)
        let fileSize = 36 + numSamples * bytesPerSample
        wavData.append(withUnsafeBytes(of: Int32(fileSize).littleEndian) { Data($0) })
        wavData.append("WAVE".data(using: .ascii)!)
        
        // Format chunk
        wavData.append("fmt ".data(using: .ascii)!)
        wavData.append(withUnsafeBytes(of: Int32(16).littleEndian) { Data($0) }) // Chunk size
        wavData.append(withUnsafeBytes(of: Int16(1).littleEndian) { Data($0) }) // Audio format (PCM)
        wavData.append(withUnsafeBytes(of: numChannels.littleEndian) { Data($0) })
        wavData.append(withUnsafeBytes(of: sampleRate.littleEndian) { Data($0) })
        let byteRate = sampleRate * Int32(numChannels) * Int32(bitsPerSample / 8)
        wavData.append(withUnsafeBytes(of: byteRate.littleEndian) { Data($0) })
        let blockAlign = numChannels * Int16(bitsPerSample / 8)
        wavData.append(withUnsafeBytes(of: blockAlign.littleEndian) { Data($0) })
        wavData.append(withUnsafeBytes(of: bitsPerSample.littleEndian) { Data($0) })
        
        // Data chunk
        wavData.append("data".data(using: .ascii)!)
        let dataSize = numSamples * bytesPerSample
        wavData.append(withUnsafeBytes(of: Int32(dataSize).littleEndian) { Data($0) })
        
        // Audio data
        for sample in int16Samples {
            wavData.append(withUnsafeBytes(of: sample.littleEndian) { Data($0) })
        }
        
        return wavData
    }
    
    private func sendAudioToAPI(_ audioData: Data) {
        guard let apiManager = apiManager else {
            DispatchQueue.main.async {
                self.isProcessing = false
                self.status = "API manager not available"
            }
            return
        }
        
        apiManager.sendAudio(audioData) { [weak self] result in
            DispatchQueue.main.async {
                switch result {
                case .success(let response):
                    self?.status = "Playing response..."
                    self?.playAudioResponse(response.audio_data)
                case .failure(let error):
                    self?.isProcessing = false
                    self?.status = "Error: \(error.localizedDescription)"
                }
            }
        }
    }
    
    private func playAudioResponse(_ base64AudioData: String) {
        guard let audioData = Data(base64Encoded: base64AudioData) else {
            DispatchQueue.main.async {
                self.isProcessing = false
                self.status = "Invalid audio response"
            }
            return
        }
        
        do {
            // Create temporary file for audio playback
            let tempURL = FileManager.default.temporaryDirectory.appendingPathComponent("response_\(UUID().uuidString).mp3")
            try audioData.write(to: tempURL)
            
            // Play audio using AVAudioPlayer
            audioPlayer = try AVAudioPlayer(contentsOf: tempURL)
            audioPlayer?.volume = 1.0
            audioPlayer?.play()
            
            // Clean up and reset state after playback
            let duration = audioPlayer?.duration ?? 2.0
            DispatchQueue.main.asyncAfter(deadline: .now() + duration + 0.5) {
                try? FileManager.default.removeItem(at: tempURL)
                self.isProcessing = false
                self.status = "Ready to record"
            }
            
        } catch {
            DispatchQueue.main.async {
                self.isProcessing = false
                self.status = "Playback failed"
            }
        }
    }
    
    func cleanup() {
        silenceTimer?.invalidate()
        audioEngine?.stop()
        inputNode?.removeTap(onBus: 0)
        audioPlayer?.stop()
    }
}
