# TinyVoice watchOS App

A simplified watchOS companion app for the TinyVoice audio chat system. This app provides a streamlined interface optimized for Apple Watch interactions.

## Features

- **Single Button Interface**: One primary button to start/stop audio recording
- **Voice Activity Detection**: Automatically stops recording when you stop speaking (2-second silence timeout)
- **Audio Processing**: Sends recorded audio to the same `/api/chat` endpoint as other frontends
- **Audio Playback**: Plays AI assistant responses through the watch speaker
- **Visual Feedback**: Simple status indicators for recording and processing states
- **No Transcription Display**: Optimized for audio-only interactions (no text display)
- **No Wake Word Support**: Manual button activation only (no "Scarlett" wake word detection)

## Requirements

- watchOS 11.5+
- Microphone permissions
- Network access to the TinyVoice backend server
- Backend server running on the same network

## Usage

1. **Start Recording**: Tap the blue microphone button to begin recording
2. **Speak Your Request**: The app will show "Recording... Speak now" status
3. **Automatic Stop**: Recording automatically stops after 2 seconds of silence or 30 seconds maximum
4. **Manual Stop**: Tap the red stop button to manually end recording
5. **Processing**: The app shows "Processing..." while sending audio to the backend
6. **Response Playback**: AI response plays automatically through the watch speaker

## Technical Details

### Audio Configuration
- **Sample Rate**: 16kHz (optimized for watchOS)
- **Format**: Mono WAV
- **Max Duration**: 30 seconds per recording
- **Silence Detection**: 2-second timeout with 0.01 amplitude threshold

### API Compatibility
- Uses the same `/api/chat` endpoint as iOS and web frontends
- Sends multipart form data with `audio_file` parameter
- Receives JSON response with base64-encoded MP3 audio

### Architecture
- **WatchAudioManager**: Handles recording, silence detection, and playback
- **WatchAPIManager**: Manages API communication with the backend
- **ContentView**: Simple SwiftUI interface optimized for watch screen

## Error Handling

The app provides visual feedback for common issues:
- Microphone access denied
- Network connectivity problems
- Server errors
- Audio processing failures

## Limitations

- No text transcription display (audio-only interface)
- No wake word detection (manual activation only)
- Simplified UI optimized for small screen
- Limited to 30-second recordings
- Requires backend server on same network

## Development Notes

- Built with SwiftUI for watchOS
- Uses AVAudioEngine for recording
- Implements custom silence detection
- Optimized for watch hardware limitations
- Compatible with existing Python backend API
