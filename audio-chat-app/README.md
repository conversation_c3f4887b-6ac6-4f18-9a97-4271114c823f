# Audio Chat Application

A multi-platform audio chat system that sends audio clips to ChatGPT's 4o-preview model and plays back audio responses. Available as a web app, iOS app, and watchOS app.

## Setup Instructions

### Prerequisites
- Python 3.8+
- uv package manager
- OpenAI API key with access to gpt-4o-audio-preview model

### Backend Setup

1. Navigate to the backend directory:
   ```bash
   cd audio-chat-app/backend
   ```

2. Install dependencies using uv:
   ```bash
   uv pip install --system fastapi uvicorn openai python-multipart python-dotenv
   ```

3. Add your OpenAI API key to the `.env` file:
   ```
   OPENAI_API_KEY=your_actual_api_key_here
   ```

4. Start the backend server:
   ```bash
   python main.py
   ```

   The server will run on `http://localhost:8000`

### Frontend Setup

1. Open a new terminal and navigate to the frontend directory:
   ```bash
   cd audio-chat-app/frontend
   ```

2. Start a simple HTTP server:
   ```bash
   python -m http.server 3000
   ```

3. Open your browser and go to `http://localhost:3000`

## Usage

1. Click "Start Recording" to begin recording audio (up to 60 seconds)
2. Click "Stop Recording" or wait for auto-stop at 60 seconds
3. Your audio will be sent to the AI automatically
4. The AI's audio response will play automatically
5. You can replay both recordings using the audio controls

## Available Frontends

### Web App
- Records audio clips up to 60 seconds
- Converts audio to WAV format for OpenAI compatibility
- Sends audio to gpt-4o-audio-preview model
- Plays back AI audio responses
- Shows transcript of AI response
- Simple, clean interface

### iOS App
- Continuous audio recording with 15-second rolling buffer
- Manual recording with "Send Last 15 Seconds" button
- Wake word detection ("Scarlett") with WebSocket connection
- Execution keyword ("skynet") for immediate processing
- Real-time transcription display
- Audio effects and feedback sounds

### watchOS App
- Single button interface optimized for Apple Watch
- Voice activity detection with automatic stop
- 30-second maximum recording duration
- Audio-only interface (no text transcription)
- Manual activation only (no wake word detection)
- Simplified UI for small screen

## Features

- Multiple frontend options for different use cases
- Unified backend API supporting all platforms
- OpenAI GPT-4o audio model integration
- Tool support (web search, Jellyfin media control)
- Wake word detection (iOS only)
- Real-time audio processing and playback